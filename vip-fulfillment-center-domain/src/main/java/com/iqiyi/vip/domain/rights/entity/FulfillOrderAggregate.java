package com.iqiyi.vip.domain.rights.entity;

import com.iqiyi.vip.domain.constraint.entity.Constraint;
import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.settlement.entity.Settlement;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.spufulfillment.entity.SpuFulfillmentConfig;
import com.iqiyi.vip.dto.order.PaidOrderInfo;
import com.iqiyi.vip.enums.ActTypeEnum;
import com.iqiyi.vip.enums.DealModuleEnum;
import com.iqiyi.vip.enums.GiftCodeEnum;
import com.iqiyi.vip.enums.TradeOrderStatusEnum;
import lombok.Data;

/**
 * 订单履约聚合根
 *
 * <AUTHOR>
 * @date 2023/8/31 13:46
 */
@Data
public class FulfillOrderAggregate {

    private Long uid;
    private String promotionCode;

    private String actCode;
    private String receiveAccount;
    private Integer actType;

    private Sku sku;
    private Gift gift;
    private SpuFulfillmentConfig spuFulfillmentConfig;
    private Constraint constraint;
    private PaidOrderInfo orderInfo;
    private Settlement settlement;
    private DealModuleEnum dealModule;

    /**
     * 是否需要履约
     */
    public boolean needFulFill() {
        if (TradeOrderStatusEnum.PRE_PAID.getStatus() == orderInfo.getStatus()) {
            //预付费订单，会员商品，只处理加价购会员，不处理其他会员订单
            if (null != gift && GiftCodeEnum.VIP_GIFT_CODE.getGiftCode().equals(gift.getCode())) {
                if (null != actType && ActTypeEnum.ADDITIONAL.getType() == actType) {
                    return true;
                } else {
                    return false;
                }
            }
        }
        return true;
    }

}
