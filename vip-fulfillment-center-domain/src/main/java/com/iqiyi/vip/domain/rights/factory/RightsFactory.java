package com.iqiyi.vip.domain.rights.factory;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iqiyi.vip.domain.constraint.factory.ConstraintFactory;
import com.iqiyi.vip.domain.fulfillment.entity.FulfillmentConfig;
import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.gift.entity.SkuGiftDTO;
import com.iqiyi.vip.domain.gift.service.GiftService;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.rights.entity.FulfillOrderAggregate;
import com.iqiyi.vip.domain.rights.entity.OpenOrderRights;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.entity.TerminateOrderAggregate;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.settlement.factory.SettlementFactory;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.spufulfillment.entity.SpuFulfillmentConfig;
import com.iqiyi.vip.domain.spufulfillment.repository.SpuFulfillmentRepository;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateDetailRes;
import com.iqiyi.vip.dto.order.OrderPaidMsg;
import com.iqiyi.vip.dto.order.PaidOrderInfo;
import com.iqiyi.vip.dto.order.RefundOrderInfo;
import com.iqiyi.vip.dto.rights.*;
import com.iqiyi.vip.dto.sku.SkuQuery;
import com.iqiyi.vip.enums.*;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.AssertUtils;
import com.iqiyi.vip.utils.DateUtils;
import com.iqiyi.vip.utils.FieldUtils;
import com.iqiyi.vip.utils.OrderUtils;
import com.qiyi.vip.trade.dataservice.client.dto.BaseDataCode;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/31 13:43
 */
@Slf4j
public class RightsFactory {

    public static FulfillOrderAggregate buildFulfillOrderAggregate(PaidOrderInfo orderInfo, Sku sku, GiftService giftService, FulfillmentConfig fulfillmentConfig, SpuFulfillmentRepository spuFulfillmentRepository, DealModuleEnum dealModule) {
        FulfillOrderAggregate fulfillOrderAggregate = new FulfillOrderAggregate();
        fulfillOrderAggregate.setUid(orderInfo.getUid());
        if (fulfillmentConfig != null) {
            fulfillOrderAggregate.setActCode(fulfillmentConfig.getActCode());
            fulfillOrderAggregate.setActType(fulfillmentConfig.getActType());
        }

        AssertUtils.notNull(sku, CodeEnum.ERROR_SKU_NULL);
        fulfillOrderAggregate.setSku(sku);

        //订单信息
        fulfillOrderAggregate.setOrderInfo(orderInfo);

        //promotionCode
        fulfillOrderAggregate.setPromotionCode(sku.getRightRecordPromotionCode());

        SkuGiftDTO skuGiftDTO = sku.obtainSkuGiftDTO(spuFulfillmentRepository);
        AssertUtils.notNull(skuGiftDTO, CodeEnum.ERROR_SYS_CONFIG);
        //spuFulfillmentConfig
        fulfillOrderAggregate.setSpuFulfillmentConfig(skuGiftDTO.getSpuFulfillmentConfig());
        //gift
        Gift gift = giftService.queryByCode(skuGiftDTO.getGiftCode());
        AssertUtils.notNull(gift, CodeEnum.ERROR_SYS_CONFIG);
        fulfillOrderAggregate.setGift(gift);

        //constraint
        fulfillOrderAggregate.setConstraint(ConstraintFactory.buildConstraint(sku, fulfillmentConfig, orderInfo));

        //结算信息
        fulfillOrderAggregate.setSettlement(SettlementFactory.buildSettlement(sku));
        fulfillOrderAggregate.setDealModule(dealModule);

        //实物履约特有的信息
        String addressCode = OrderUtils.getAddressCode(orderInfo.getRefer());
        if (StringUtils.isNotBlank(addressCode)) {
            fulfillOrderAggregate.setReceiveAccount(addressCode);
        }
        return fulfillOrderAggregate;
    }

    public static OpenOrderRights buildOpenPartnerRightsReq(FulfillOrderAggregate fulfillOrderAggregate) {
        OpenOrderRights openOrderRights = new OpenOrderRights();
        BeanUtils.copyProperties(fulfillOrderAggregate, openOrderRights);
        openOrderRights.setOrderInfo(buildOpenPartnerRightsOrderReq(fulfillOrderAggregate.getOrderInfo()));
        return openOrderRights;
    }

    /**
     * 组织请求权益发放模块 开通权益的参数
     */
    public static OpenPartnerRightsReq buildOpenPartnerRightsReq(OpenOrderRights openRights) {
        OpenPartnerRightsReq openPartnerRightsReq = new OpenPartnerRightsReq();
        openPartnerRightsReq.setUid(openRights.getUid());
        openPartnerRightsReq.setOrderCode(openRights.getOrderInfo().getOrderCode());
        String receiveAccount = openRights.getReceiveAccount();
        if (!YesOrNoEnum.YES.getValue().equals(openRights.getSku().getSpecAttributes().getChangeAccount())
            && StringUtils.isNotBlank(receiveAccount)) {
            //不支持切换账号，调用方又传入了receiveAccount
            log.error("[not support change account][req:{}]", openRights);
            throw new BizRuntimeException(CodeEnum.ACCOUNT_NOT_SUPPORT);
        }
        openPartnerRightsReq.setReceiveAccount(receiveAccount);
        openPartnerRightsReq.setPromotionCode(openRights.getPromotionCode());
        openPartnerRightsReq.setSendNotifyUrlId(openRights.getGift().getSendNotifyUrlId());
        Map sendExtendReqParamsMap = getSendExtendReqParamsMap(openRights.getSpuFulfillmentConfig(), openRights.getSku(), openRights.getOrderInfo());
        if (MapUtils.isNotEmpty(sendExtendReqParamsMap)) {
            openPartnerRightsReq.setExtendReqParamsMap(JSON.toJSONString(sendExtendReqParamsMap));
        }
        return openPartnerRightsReq;
    }

    /**
     * 组织请求开通VMC接口的请求参数
     */
    public static OpenVmcRightsReq buildOpenVmcRightsReq(OpenPartnerRightsOrderReq orderInfo) {
        //透传订单信息
        OpenVmcRightsReq openVmcRightsReq = new OpenVmcRightsReq();
        openVmcRightsReq.setUserId(orderInfo.getUid());
        openVmcRightsReq.setOrderCode(orderInfo.getOrderCode());
        openVmcRightsReq.setOrderStatus(orderInfo.getStatus());
        openVmcRightsReq.setSkuId(orderInfo.getSkuId());
        openVmcRightsReq.setSkuAmount(null == orderInfo.getSkuAmount() ? 1 : orderInfo.getSkuAmount());

        //设置单点影片内容
        if (hasValidBusinessValues(orderInfo.getBusinessValues())) {
            openVmcRightsReq.setContentId(Long.valueOf(orderInfo.getBusinessValues()));
        } else if (null != orderInfo.getContentId()) {
            openVmcRightsReq.setContentId(orderInfo.getContentId());
        }

        openVmcRightsReq.setChargeType(orderInfo.getChargeType());
        if (StringUtils.isNotBlank(orderInfo.getPartner())) {
            openVmcRightsReq.setPartner(orderInfo.getPartner());
        }
        openVmcRightsReq.setProductCode(orderInfo.getProductCode());
        openVmcRightsReq.setAmount(orderInfo.getAmount());
        return openVmcRightsReq;
    }

    public static Map getSendExtendReqParamsMap(SpuFulfillmentConfig spuFulfillmentConfig, Sku sku, OpenPartnerRightsOrderReq orderInfo) {
        if (null == spuFulfillmentConfig) {
            return null;
        }
        Map<String, Object> sendExtendReqParamsMap = Maps.newHashMap();
        FieldUtils.extracted(spuFulfillmentConfig.getSkuParams(), sendExtendReqParamsMap, JSON.toJSONString(sku));
        FieldUtils.extracted(spuFulfillmentConfig.getOrderParams(), sendExtendReqParamsMap, JSON.toJSONString(orderInfo));
        return sendExtendReqParamsMap;
    }

    public static AskPartnerCanBuyByUrlIdDetailReq buildAskPartnerReq(SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate) {
        AskPartnerCanBuyByUrlIdDetailReq req = new AskPartnerCanBuyByUrlIdDetailReq();
        req.setUrlId(skuBeforeBuyCheckAggregate.getGift().getBeforeCheckUrlId());
        Map extendReqParamsMap = RightsFactory.getAskPartnerReqParamsMap(skuBeforeBuyCheckAggregate.getSpuFulfillmentConfig(), skuBeforeBuyCheckAggregate.getSku());
        if (MapUtils.isNotEmpty(extendReqParamsMap)) {
            req.setExtendReqParamsMap(JSON.toJSONString(extendReqParamsMap));
        }
        return req;
    }

    public static Map getAskPartnerReqParamsMap(SpuFulfillmentConfig spuFulfillmentConfig, Sku sku) {
        if (null == spuFulfillmentConfig) {
            return null;
        }
        Map<String, Object> sendExtendReqParamsMap = Maps.newHashMap();
        FieldUtils.extracted(spuFulfillmentConfig.getBeforeCheckSkuParams(), sendExtendReqParamsMap, JSON.toJSONString(sku));
        return sendExtendReqParamsMap;
    }

    public static PaidOrderInfo buildPaidOrderInfo(OrderPaidMsg orderPaidMsg) {
        //初始化重试次数
        orderPaidMsg.setCurrentRetryCount(0);
        PaidOrderInfo paidOrderInfo = new PaidOrderInfo();
        BeanUtils.copyProperties(orderPaidMsg, paidOrderInfo);
        paidOrderInfo.setUid(orderPaidMsg.getUserId());
        paidOrderInfo.setFrVersion(StringUtils.isNotEmpty(orderPaidMsg.getFrVersion()) ? orderPaidMsg.getFrVersion() : orderPaidMsg.getFr_version());
        return paidOrderInfo;
    }

    public static PaidOrderInfo buildPaidOrderInfo(String orderCode, OrderService orderService) {
        OrderDto orderDto = orderService.queryByOrderCode(orderCode);
        if (null == orderDto) {
            log.error("[order null][orderCode:{}]", orderCode);
            throw new BizRuntimeException(CodeEnum.ERROR_RECEIVE_NOT_EXISTS);
        }
        OrderDto refundOrder = orderService.queryByTradeCode(orderCode);
        if (null != refundOrder) {
            log.error("[order is refund][refundOrderCode:{},paidOrderCode:{}]", refundOrder.getOrderCode(), orderCode);
            throw new BizRuntimeException(CodeEnum.ERROR_IS_REFUND);
        }

        return buildByOrderDto(orderDto);
    }

    public static PaidOrderInfo buildByOrderDto(OrderDto orderDto) {
        BaseDataCode baseDataCode = orderDto.getBaseDataCode();
        String productCode = null == baseDataCode ? "" : baseDataCode.getProductCode();
        return PaidOrderInfo.builder()
            .uid(orderDto.getUserId())
            .skuId(orderDto.getSkuId())
            .skuAmount(orderDto.getSkuAmount())
            .orderCode(orderDto.getOrderCode())
            .payTime(orderDto.getPayTime())
            .status(orderDto.getStatus())
            .realFee(Long.valueOf(orderDto.getRealFee()))
            .parentOrderCode(orderDto.getParentOrderCode())
            .tradeNo(orderDto.getTradeNo())
            .refer(orderDto.getRefer())
            .tradeCode(orderDto.getTradeCode())
            .platformCode((orderDto.getBaseDataCode() != null) ? orderDto.getBaseDataCode().getPlatformCode() : null)
            .frVersion(orderDto.getFrVersion())
            .fv(orderDto.getFv())
            .gateway(orderDto.getGateway())
            .chargeType(orderDto.getChargeType())
            .partner(orderDto.getPartner())
            .amount(orderDto.getAmount())
            .contentId(orderDto.getContentId())
            .productCode(productCode)
            .build();
    }

    public static OpenPartnerRightsOrderReq buildOpenPartnerRightsOrderReq(String orderCode, OrderService orderService) {
        OrderDto orderDto = orderService.queryByOrderCode(orderCode);
        if (null == orderDto) {
            return null;
        }
        return buildOpenPartnerRightsOrderReq(buildByOrderDto(orderDto));
    }

    public static OpenPartnerRightsOrderReq buildOpenPartnerRightsOrderReq(PaidOrderInfo orderInfo) {
        OpenPartnerRightsOrderReq openPartnerRightsOrderReq = new OpenPartnerRightsOrderReq();
        BeanUtils.copyProperties(orderInfo, openPartnerRightsOrderReq);
        openPartnerRightsOrderReq.setSellScene(OrderUtils.getSellScene(orderInfo.getRefer()));
        return openPartnerRightsOrderReq;
    }

    public static PartnerRightsResponse buildOpenPartnerRightsRes(ReceiveRightRecord receiveRightRecord) {
        PartnerRightsResponse res = new PartnerRightsResponse();
        res.setSendRes(receiveRightRecord.getSendRes());
        res.setSendStatus(receiveRightRecord.getSendStatus());
        res.setCouponCode(receiveRightRecord.getCouponCode());
        res.setAccount(receiveRightRecord.getEncryptAccount());
        res.setMobile(receiveRightRecord.getEncryptMobile());
        res.setAmount(receiveRightRecord.getAmount());
        return res;
    }

    public static PartnerRightsResponse buildRefundPartnerRightsRes(ReceiveRightRecord receiveRightRecord) {
        PartnerRightsResponse res = new PartnerRightsResponse();
        if (null != receiveRightRecord) {
            res.setSendRes(receiveRightRecord.getSendRes());
            res.setSendStatus(receiveRightRecord.getSendStatus());
        }
        return res;
    }

    public static BaseResponse createOpenRightsRes(CodeEnum codeEnum) {
        BaseResponse response = new BaseResponse(codeEnum);
        Integer retry = BooleanUtils.toIntegerObject(CodeEnum.openRightsNeedRetryCodeEnumList().contains(codeEnum));
        response.setRetry(retry);
        return response;
    }

    public static BaseResponse createTerminateRightsRes(CodeEnum codeEnum, ReceiveRightRecord receiveRightRecord) {
        BaseResponse response = new BaseResponse(codeEnum);
        Integer retry = BooleanUtils.toIntegerObject(CodeEnum.terminateRightsNeedRetryCodeEnumList().contains(codeEnum));
        response.setRetry(retry);
        response.setData(buildRefundPartnerRightsRes(receiveRightRecord));
        return response;
    }

    public static CheckOrderCanTerminateDetailRes buildCheckOrderCanTerminateDetailRes(String orderCode, CodeEnum checkCodeMsgEnum) {
        if (CodeEnum.checkOrderCanTerminateCanRefund().contains(checkCodeMsgEnum)) {
            return new CheckOrderCanTerminateDetailRes(orderCode, checkCodeMsgEnum, YesOrNoEnum.YES.getValue());
        }
        return new CheckOrderCanTerminateDetailRes(orderCode, checkCodeMsgEnum);
    }

    public static List<OpenOrderRights> buildByReceiveRights(ReceiveRightsReq receiveRightsReq, SkuRepository skuRepository, ReceiveRecordRepository receiveRecordRepository, OrderService orderService, SpuFulfillmentRepository spuFulfillmentRepository, GiftService giftService) {
        AssertUtils.notNull(receiveRightsReq.getUid(), CodeEnum.ERROR_NOT_LOG);

        //商品
        AssertUtils.notBlank(receiveRightsReq.getSkuId(), CodeEnum.ERROR_PARAM);
        Sku sku = skuRepository.queryFromCache(SkuQuery.builder().skuId(receiveRightsReq.getSkuId()).onlyQueryValid(false).build());
        AssertUtils.notNull(sku, CodeEnum.ERROR_SKU_NULL);

        //打包购商品
        if (SpuEnum.isPackageSpu(sku.getSpuId())) {
            List<Sku> subSkuList = sku.getSubSkuList();
            AssertUtils.notEmpty(subSkuList, CodeEnum.ERROR_SKU_NULL);
            List<OpenOrderRights> openOrderRightsList = Lists.newArrayList();
            for (Sku subSku : subSkuList) {
                openOrderRightsList.addAll(buildByReceiveRights(receiveRightsReq, subSku, receiveRecordRepository, orderService, spuFulfillmentRepository, giftService));
            }
            return openOrderRightsList;
        } else {
            return buildByReceiveRights(receiveRightsReq, sku, receiveRecordRepository, orderService, spuFulfillmentRepository, giftService);
        }
    }

    public static List<OpenOrderRights> buildByReceiveRights(ReceiveRightsReq receiveRightsReq, Sku sku, ReceiveRecordRepository receiveRecordRepository, OrderService orderService, SpuFulfillmentRepository spuFulfillmentRepository, GiftService giftService) {
        OpenOrderRights openOrderRightsBaseInfo = new OpenOrderRights();
        openOrderRightsBaseInfo.setReceiveAccount(receiveRightsReq.getReceiveAccount());
        AssertUtils.notNull(receiveRightsReq.getUid(), CodeEnum.ERROR_NOT_LOG);
        openOrderRightsBaseInfo.setUid(receiveRightsReq.getUid());

        //商品
        AssertUtils.notBlank(receiveRightsReq.getSkuId(), CodeEnum.ERROR_PARAM);
        AssertUtils.notNull(sku, CodeEnum.ERROR_SKU_NULL);
        openOrderRightsBaseInfo.setSku(sku);

        //promotionCode
        openOrderRightsBaseInfo.setPromotionCode(sku.getRightRecordPromotionCode());

        SkuGiftDTO skuGiftDTO = sku.obtainSkuGiftDTO(spuFulfillmentRepository);
        AssertUtils.notNull(skuGiftDTO, CodeEnum.ERROR_SYS_CONFIG);
        //spuFulfillmentConfig
        openOrderRightsBaseInfo.setSpuFulfillmentConfig(skuGiftDTO.getSpuFulfillmentConfig());

        //gift
        Gift gift = giftService.queryByCode(skuGiftDTO.getGiftCode());
        AssertUtils.notNull(gift, CodeEnum.ERROR_SYS_CONFIG);
        openOrderRightsBaseInfo.setGift(gift);

        //结算信息
        openOrderRightsBaseInfo.setSettlement(SettlementFactory.buildSettlement(sku));

        //订单信息
        List<OpenOrderRights> openOrderRightsList = Lists.newArrayList();
        ReceiveRecordByOrderCodeQry receiveRecordByorderCodeQry = ReceiveRecordByOrderCodeQry.builder()
            .uid(openOrderRightsBaseInfo.getUid())
            .orderCode(receiveRightsReq.getOrderCode())
            .promotionCode(openOrderRightsBaseInfo.getPromotionCode())
            .build();

        //按订单领取,检查订单状态
        if (StringUtils.isNotBlank(receiveRightsReq.getOrderCode())) {
            ReceiveRightRecordQryCon orderQueryCon = ReceiveRightRecordQryCon.builder()
                .uid(openOrderRightsBaseInfo.getUid())
                .orderCode(receiveRightsReq.getOrderCode())
                .promotionCode(openOrderRightsBaseInfo.getPromotionCode()).build();
            checkReceiveByOrderCode(orderQueryCon, receiveRecordRepository);
        }

        List<OpenPartnerRightsOrderReq> canReceiveOrderList = queryCanReceiveOrderList(receiveRecordByorderCodeQry, receiveRecordRepository, orderService);
        for (OpenPartnerRightsOrderReq orderReq : canReceiveOrderList) {
            OpenOrderRights openRights = new OpenOrderRights();
            BeanUtils.copyProperties(openOrderRightsBaseInfo, openRights);
            openRights.setOrderInfo(orderReq);
            openOrderRightsList.add(openRights);
        }
        return openOrderRightsList;
    }

    /**
     * 校验领取订单状态
     */
    public static void checkReceiveByOrderCode(ReceiveRightRecordQryCon con, ReceiveRecordRepository receiveRecordRepository) {
        if (StringUtils.isBlank(con.getOrderCode())) {
            return;
        }
        List<ReceiveRightRecord> recordList = receiveRecordRepository.queryByConFromMySql(con);
        AssertUtils.notEmpty(recordList, CodeEnum.ERROR_ORDER_NON);
        ReceiveRightRecord order = recordList.get(0);
        if (null != order.getReceiveStatus() && ReceiveStatusEnum.RECEIVED.getStatus() == order.getReceiveStatus()) {
            throw new BizRuntimeException(CodeEnum.ERROR_ORDER_IS_RECEIVED);
        }
        if (null != order.getRefundStatus() && ReceiveRightRecordRefundStatus.UN_REFUND.getStatus() != order.getRefundStatus()) {
            throw new BizRuntimeException(CodeEnum.ERROR_ORDER_REFUND);
        }
        if (null != order.getReceiveDeadlineTime() && !DateUtils.verifyReceiveDeadlineTime(order.getReceiveDeadlineTime())) {
            throw new BizRuntimeException(CodeEnum.ERROR_ORDER_RECEIVE_EXPIRE);
        }
    }

    public static List<OpenPartnerRightsOrderReq> queryCanReceiveOrderList(ReceiveRecordByOrderCodeQry receiveRecordByorderCodeQry, ReceiveRecordRepository receiveRecordRepository, OrderService orderService) {
        List<ReceiveRightRecord> canReceiveRightsList = receiveRecordRepository.queryCanReceiveOrderList(receiveRecordByorderCodeQry);
        if (CollectionUtils.isEmpty(canReceiveRightsList)) {
            return Collections.EMPTY_LIST;
        }

        List<OpenPartnerRightsOrderReq> orderComponentDTOList = Lists.newArrayList();
        for (ReceiveRightRecord receiveRightRecord : canReceiveRightsList) {
            OpenPartnerRightsOrderReq orderReq = buildOpenPartnerRightsOrderReq(receiveRightRecord.getOrderCode(), orderService);
            AssertUtils.notNull(orderReq, CodeEnum.ERROR_RECEIVE_NOT_EXISTS);
            orderComponentDTOList.add(orderReq);
        }
        return orderComponentDTOList;
    }

    public static RefundOrderInfo buildRefundOrderInfo(String refundOrderCode, OrderService orderService) {
        AssertUtils.notNull(refundOrderCode, CodeEnum.ERROR_PARAM);
        OrderDto refundOrder = orderService.queryByOrderCode(refundOrderCode);
        if (null == refundOrder) {
            throw new BizRuntimeException(CodeEnum.ERROR_RECEIVE_NOT_EXISTS);
        }
        if (!TradeOrderStatusEnum.isRefundStatus(refundOrder.getStatus())) {
            throw new BizRuntimeException(CodeEnum.NOT_REFUND_ORDER);
        }
        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        refundOrderInfo.setUid(refundOrder.getUserId());
        refundOrderInfo.setSkuId(refundOrder.getSkuId());
        refundOrderInfo.setSkuAmount(refundOrder.getSkuAmount());
        refundOrderInfo.setPaidOrderCode(refundOrder.getTradeCode());
        refundOrderInfo.setRefundOrderCode(refundOrder.getOrderCode());
        refundOrderInfo.setStatus(refundOrder.getStatus());
        refundOrderInfo.setRefundTime(refundOrder.getPayTime());
        refundOrderInfo.setRefer(refundOrder.getRefer());
        refundOrderInfo.setTradeNo(refundOrder.getTradeNo());
        refundOrderInfo.setTradeCode(refundOrder.getTradeCode());
        refundOrderInfo.setPlatformCode((refundOrder.getBaseDataCode() != null) ? refundOrder.getBaseDataCode().getPlatformCode() : null);
        refundOrderInfo.setSellScene(OrderUtils.getSellScene(refundOrder.getRefer()));
        return refundOrderInfo;
    }

    public static RefundOrderInfo buildRefundOrderInfo(OrderPaidMsg orderPaidMsg) {
        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        refundOrderInfo.setUid(orderPaidMsg.getUserId());
        refundOrderInfo.setSkuId(orderPaidMsg.getSkuId());
        refundOrderInfo.setSkuAmount(orderPaidMsg.getSkuAmount());
        refundOrderInfo.setPaidOrderCode(orderPaidMsg.getTradeCode());
        refundOrderInfo.setRefundOrderCode(orderPaidMsg.getOrderCode());
        refundOrderInfo.setStatus(orderPaidMsg.getStatus());
        refundOrderInfo.setRefundTime(orderPaidMsg.getPayTime());
        refundOrderInfo.setRefer(orderPaidMsg.getRefer());
        refundOrderInfo.setTradeNo(orderPaidMsg.getTradeNo());
        refundOrderInfo.setTradeCode(orderPaidMsg.getTradeCode());
        refundOrderInfo.setPlatformCode(orderPaidMsg.getPlatformCode());
        refundOrderInfo.setSellScene(OrderUtils.getSellScene(orderPaidMsg.getRefer()));
        return refundOrderInfo;
    }

    public static TerminateOrderAggregate buildTerminateOrderAggregate(RefundOrderInfo refundOrderInfo, SkuRepository skuRepository, FulfillmentConfig fulfillmentConfig) {
        TerminateOrderAggregate terminateOrderAggregate = new TerminateOrderAggregate();
        terminateOrderAggregate.setRefundOrderInfo(refundOrderInfo);

        //商品
        Sku sku = skuRepository.queryFromCache(SkuQuery.builder().skuId(refundOrderInfo.getSkuId()).onlyQueryValid(false).build());
        AssertUtils.notNull(sku, CodeEnum.ERROR_SKU_NULL);
        terminateOrderAggregate.setPromotionCode(sku.getRightRecordPromotionCode());
        terminateOrderAggregate.setSku(sku);
        terminateOrderAggregate.setRefundConstraint(ConstraintFactory.buildRefundConstraint(sku, fulfillmentConfig));
        return terminateOrderAggregate;
    }

    public static TerminateRightsReq buildRefundPartnerRightsReq(TerminateOrderAggregate terminateOrderAggregate, ReceiveRightRecord paidRightRecord) {
        TerminateRightsReq terminateRightsReq = new TerminateRightsReq();
        RefundOrderInfo refundOrderInfo = terminateOrderAggregate.getRefundOrderInfo();
        terminateRightsReq.setUid(refundOrderInfo.getUid());
        terminateRightsReq.setOrderCode(refundOrderInfo.getPaidOrderCode());
        terminateRightsReq.setSkuId(refundOrderInfo.getSkuId());
        terminateRightsReq.setCouponCode(paidRightRecord.getCouponCode());
        terminateRightsReq.setRefundOrderCode(paidRightRecord.getRefundCode());
        terminateRightsReq.setRightsSendTime(null == paidRightRecord.getSendTime() ? null : paidRightRecord.getSendTime().getTime());
        terminateRightsReq.setReceiveAccount(paidRightRecord.getEncryptAccount());
        return terminateRightsReq;
    }

    private static boolean hasValidBusinessValues(String businessValues) {
        return !"null".equals(businessValues) && StringUtils.isNotBlank(businessValues) && NumberUtils.isDigits(businessValues);
    }
}
