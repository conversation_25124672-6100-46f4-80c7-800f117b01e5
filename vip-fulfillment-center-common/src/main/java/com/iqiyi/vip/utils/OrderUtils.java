package com.iqiyi.vip.utils;

import com.google.common.collect.Maps;
import com.iqiyi.vip.dto.order.OrderReferMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Map;


/**
 * <AUTHOR>
 * @date 2022/7/18 13:38
 */
@Slf4j
public class OrderUtils {

    /**
     * 获取订单中的地址id http://wiki.qiyi.domain/pages/viewpage.action?pageId=29426430
     */
    public static String getAddressCode(String refer) {
        try {
            if (StringUtils.isBlank(refer)) {
                return null;
            }
            OrderReferMsg orderRefer = JacksonUtil.json2obj(refer, OrderReferMsg.class);
            if (null != orderRefer && null != orderRefer.getBusinessProperty()) {
                return orderRefer.getBusinessProperty().getAddressCode();
            }
        } catch (Exception e) {
            log.error("refer:{}", refer, e);
        }
        return null;
    }

    /**
     * 获取订单中加价购活动code码 http://wiki.qiyi.domain/pages/viewpage.action?pageId=29426430
     */
    public static String getCombineActCode(String refer) {
        try {
            if (StringUtils.isBlank(refer)) {
                return null;
            }
            OrderReferMsg orderRefer = JacksonUtil.json2obj(refer, OrderReferMsg.class);
            if (null != orderRefer && null != orderRefer.getBusinessProperty()) {
                return orderRefer.getBusinessProperty().getCombineActCode();
            }
        } catch (Exception e) {
            log.error("refer:{}", refer, e);
        }
        return null;
    }

    /**
     * 获取订单中售卖场景值 http://wiki.qiyi.domain/pages/viewpage.action?pageId=29426430
     */
    public static String getSellScene(String orderMsgRefer) {
        try {
            if (StringUtils.isBlank(orderMsgRefer)) {
                return null;
            }
            OrderReferMsg orderReferDTO = JacksonUtil.json2obj(orderMsgRefer, OrderReferMsg.class);
            if (null != orderReferDTO && null != orderReferDTO.getBusinessProperty()) {
                return orderReferDTO.getBusinessProperty().getSellScene();
            }
        } catch (Exception e) {
            log.error("orderMsgRefer:{}", orderMsgRefer, e);
        }
        return null;
    }

    public static String getActCenterRuleCode(String orderMsgRefer) {
        try {
            if (StringUtils.isBlank(orderMsgRefer)) {
                return null;
            }
            OrderReferMsg orderReferDTO = JacksonUtil.json2obj(orderMsgRefer, OrderReferMsg.class);
            if (null != orderReferDTO && null != orderReferDTO.getBusinessProperty()) {
                return orderReferDTO.getBusinessProperty().getActCenterRuleCode();
            }
        } catch (Exception e) {
            log.error("orderMsgRefer:{}", orderMsgRefer, e);
        }
        return null;
    }

    public static String getFromFrVersion(String frVersion, String key) {
        if (org.apache.commons.lang3.StringUtils.isBlank(frVersion)) {
            return null;
        }
        Map<String, String> params = Maps.newHashMap();
        try {
            String[] paramsArr = frVersion.split("&");
            if (paramsArr.length == 0) {
                return null;
            }
            for (String paramStr : paramsArr) {
                if (paramStr.contains("=")) {
                    String[] paramArr = paramStr.split("=");
                    if (paramArr.length == 2) {
                        params.put(paramArr[0], paramArr[1]);
                    }
                }
            }
        } catch (Exception e) {
            log.info("An error happened when split fr_version {}", frVersion, e);
        }
        return params.get(key);
    }

    public static String getModeType(String refer) {
        try {
            if (StringUtils.isBlank(refer)) {
                return null;
            }
            OrderReferMsg orderRefer = JacksonUtil.json2obj(refer, OrderReferMsg.class);
            if (null != orderRefer) {
                return orderRefer.getModeType();
            }
        } catch (Exception e) {
            log.error("refer:{}", refer, e);
        }
        return null;
    }

    /**
     * 解析库存中心活动码
     */
    public static String getStockActCode(String refer) {
        try {
            if (StringUtils.isBlank(refer)) {
                return null;
            }
            OrderReferMsg orderRefer = JacksonUtil.json2obj(refer, OrderReferMsg.class);
            if (null != orderRefer && null != orderRefer.getBusinessProperty()) {
                return orderRefer.getBusinessProperty().getStockActCode();
            }
        } catch (Exception e) {
            log.error("refer:{}", refer, e);
        }
        return null;
    }
}
