package com.iqiyi.vip.utils;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 发货SPU工具类
 * 用于判断SPU是否需要发货
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@Component
public class ShippingSpuUtils {

    @Value("${needShippingSpuIds:spu_shopping_union_v2}")
    private String needShippingSpuIds;

    /**
     * 判断指定的SPU是否需要发货
     * 
     * @param spuId SPU ID
     * @return true表示需要发货，false表示不需要发货
     */
    public boolean isNeedShippingSpu(String spuId) {
        if (StringUtils.isBlank(spuId) || StringUtils.isBlank(needShippingSpuIds)) {
            return false;
        }
        
        List<String> needShippingSpuIdList = Arrays.asList(needShippingSpuIds.split(","));
        return needShippingSpuIdList.contains(spuId);
    }

}
