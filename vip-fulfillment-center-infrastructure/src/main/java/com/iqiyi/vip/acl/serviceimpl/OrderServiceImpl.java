package com.iqiyi.vip.acl.serviceimpl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.iqiyi.vip.domain.order.entity.CheckCanTerminateRightsAggregate;
import com.iqiyi.vip.domain.order.entity.CheckOrderCanTerminateDetail;
import com.iqiyi.vip.domain.order.entity.FreePayResult;
import com.iqiyi.vip.domain.order.factory.CheckCanTerminateFactory;
import com.iqiyi.vip.domain.order.repository.OrderRepository;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.factory.RightRecordRemarkFactory;
import com.iqiyi.vip.domain.rights.factory.RightsFactory;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.rights.service.RightsService;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.sku.service.SkuService;
import com.iqiyi.vip.domain.task.PaidOrderCallBackTask;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateDetailRes;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateRightsReq;
import com.iqiyi.vip.dto.rights.OrderDeliverReq;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;
import com.iqiyi.vip.dto.sku.SkuQuery;
import com.iqiyi.vip.enums.CallBackStatusEnum;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.SendStatusEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import com.iqiyi.vip.utils.AssertUtils;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/12 21:02
 */
@Slf4j
@Service("orderService")
public class OrderServiceImpl implements OrderService {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private ReceiveRecordRepository receiveRecordRepository;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuService skuService;
    @Resource
    private RightsService rightsService;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Value("${needShippingSpuIds:spu_shopping_union_v2}")
    private String needShippingSpuIds;

    @Override
    public boolean paidOrderCallBack(ReceiveRecordByOrderCodeQry query) {
        ReceiveRightRecord rightRecord = receiveRecordRepository.queryByOrderCode(query);
        if (null == rightRecord) {
            log.error("[rightRecord null][reqParams:{}]", query);
            return false;
        }
        return paidOrderCallBack(rightRecord);
    }

    @Override
    public boolean paidOrderCallBackFailRetry(ReceiveRecordByOrderCodeQry query) {
        PaidOrderCallBackTask paidOrderCallBackTask = new PaidOrderCallBackTask(query);
        boolean exeSuc = paidOrderCallBackTask.execute();
        if (!exeSuc) {
            log.warn("[sync paidOrderCallBackTask fail][paidOrderCallBackTask:{}]", paidOrderCallBackTask);
            clusterAsyncTaskManager.insertTask(paidOrderCallBackTask);
        }
        return false;
    }

    @Override
    public boolean paidOrderCallBack(RecordByOrderCodeSkuIdQry query) {
        ReceiveRightRecord rightRecord = receiveRecordRepository.queryByOrderCode(query);
        if (null == rightRecord) {
            log.error("[rightRecord null][reqParams:{}]", query);
            return false;
        }
        return paidOrderCallBack(rightRecord);
    }

    public boolean paidOrderCallBack(ReceiveRightRecord rightRecord) {
        if (!SendStatusEnum.isSyncSuccess(rightRecord.getSendStatus())) {
            log.error("[send fail][rightRecord:{}]", rightRecord);
            return false;
        }
        //正单号回调
        OrderDeliverReq req = RightRecordRemarkFactory.buildOrderDeliverReq(rightRecord, rightRecord.getRemark());
        //填充发货信息到refer中
        fillShippingInfoToRefer(req, rightRecord);

        HttpResDTO resDTO = orderRepository.deliver(req);
        if (null == resDTO || !CodeEnum.SUCCESS.getCode().equals(resDTO.getCode())) {
            log.error("[callBack fail][rightRecord:{}]", rightRecord);
            return false;
        }
        rightRecord.setCallBackStatus(CallBackStatusEnum.PAID_CALL_SUC.getStatus());
        int updateSize = receiveRecordRepository.updateCallBackStatus(rightRecord);
        log.info("[callBack success][rightRecord:{},updateSize:{}]", rightRecord, updateSize);
        return true;
    }

    @Override
    public boolean refundOrderCallBack(ReceiveRecordByOrderCodeQry query) {
        ReceiveRightRecord rightRecord = receiveRecordRepository.queryByOrderCode(query);
        if (null == rightRecord) {
            log.error("[rightRecord null][reqParams:{}]", query);
            return false;
        }
        //退单号回调 todo 目前会员退权益还不在履约中心，所以先不用传其他参数给交易
        OrderDeliverReq req = new OrderDeliverReq();
        req.setOrderCode(rightRecord.getRefundCode());
        HttpResDTO resDTO = orderRepository.deliver(req);
        if (null == resDTO || !CodeEnum.SUCCESS.getCode().equals(resDTO.getCode())) {
            log.error("[callBack fail][reqParams:{}]", query);
            return false;
        }
        rightRecord.setCallBackStatus(CallBackStatusEnum.REFUND_CALL_SUC.getStatus());
        int updateSize = receiveRecordRepository.updateCallBackStatus(rightRecord);
        log.info("[callBack success][reqParams:{},updateSize:{}]", query, updateSize);
        return true;
    }

    /**
     * 查询订单平台
     */
    @Override
    public String queryOrderPlatformCode(String orderCode) {
        try {
            OrderDto orderDto = orderRepository.queryByOrderCode(orderCode);
            return null != orderDto && null != orderDto.getBaseDataCode() ? orderDto.getBaseDataCode().getPlatformCode() : "";
        } catch (Exception e) {
            log.error("[Exception][orderCode:{}]", orderCode, e);
            return "";
        }
    }

    @Override
    public OrderDto queryByOrderCode(String orderCode) {
        try {
            if (StringUtils.isBlank(orderCode)) {
                log.warn("[orderCode null]");
                return null;
            }
            return orderRepository.queryByOrderCode(orderCode);
        } catch (Exception e) {
            log.error("[Exception][orderCode:{}]", orderCode, e);
            return null;
        }
    }

    @Override
    public OrderDto queryByTradeCode(String tradeCode) {
        try {
            if (StringUtils.isBlank(tradeCode)) {
                log.warn("[tradeCode null]");
                return null;
            }
            return orderRepository.queryByTradeCode(tradeCode);
        } catch (Exception e) {
            log.error("[Exception][tradeCode:{}]", tradeCode, e);
            return null;
        }
    }

    @Override
    public BaseResponse checkOrderCanTerminateRights(CheckOrderCanTerminateRightsReq req) {
        CheckCanTerminateRightsAggregate aggregate = CheckCanTerminateFactory.buildCheckCanTerminateRightsAggregate(req, skuRepository);
        List<CheckOrderCanTerminateDetail> detailList = CheckCanTerminateFactory.buildCheckOrderCanTerminateDetail(aggregate);

        List<CheckOrderCanTerminateDetailRes> resList = Lists.newArrayList();
        for (CheckOrderCanTerminateDetail detail : detailList) {
            resList.add(checkOrderCanTerminateRights(detail));
        }
        return BaseResponse.createSuccessResponse(resList);
    }

    @Override
    public FreePayResult freePay(String skuId, OrderDto order, String traceId) {
        if(StringUtils.isBlank(skuId) || null == order.getUserId() || StringUtils.isBlank(traceId)) {
            log.error("[freePay][skuId:{},uid:{},traceId:{}]", skuId, order.getUserId(), traceId);
            throw new BizRuntimeException(CodeEnum.ERROR_PARAM);
        }
        return orderRepository.freePay(skuId, order, traceId);
    }

    public CheckOrderCanTerminateDetailRes checkOrderCanTerminateRights(CheckOrderCanTerminateDetail req) {
        try {
            AssertUtils.notBlank(req.getReq().getOrderCode(), CodeEnum.ERROR_PARAM);
            AssertUtils.notBlank(req.getReq().getSkuId(), CodeEnum.ERROR_PARAM);
            AssertUtils.notNull(req.getReq().getUid(), CodeEnum.ERROR_PARAM);

            //1.验证商品是否配置成允许回收
            if (!YesOrNoEnum.YES.equals(skuService.canOnlineRefund(req.getSku()))) {
                throw new BizRuntimeException(CodeEnum.ERROR_CAN_NOT_REFUND);
            }
            //2.验证权益能否退，例如代金券订单,用户已经使用了代金券,就不能退
            return rightsService.checkCanTerminateRights(req);
        } catch (BizRuntimeException e) {
            return RightsFactory.buildCheckOrderCanTerminateDetailRes(req.getReq().getOrderCode(), e.getCodeEnum());
        } catch (Exception e) {
            log.error("[req:{}]", req, e);
            return RightsFactory.buildCheckOrderCanTerminateDetailRes(req.getReq().getOrderCode(), CodeEnum.ERROR_SYSTEM);
        }
    }

    /**
     * 填充发货信息到refer中
     */
    private void fillShippingInfoToRefer(OrderDeliverReq req, ReceiveRightRecord rightRecord) {
        Sku sku = skuRepository.queryFromCache(SkuQuery.builder()
                .skuId(rightRecord.getSkuId())
                .onlyQueryValid(false)
                .build());

        // 如果是需要发货的品类，则回写商城订单和地址id等信息
        if (sku != null && StringUtils.isNotBlank(sku.getSpuId())) {
            List<String> needShippingSpuIdList = Arrays.asList(needShippingSpuIds.split(","));
            if (needShippingSpuIdList.contains(sku.getSpuId())) {
                Map<String, Object> refer = req.getRefer();
                if (refer == null) {
                    refer = Maps.newHashMap();
                    req.setRefer(refer);
                }

                if (StringUtils.isNotBlank(rightRecord.getCouponCode())) {
                    refer.put("businessProperty.mallOrderCode", rightRecord.getCouponCode());
                }
                if (StringUtils.isNotBlank(rightRecord.getEncryptAccount())) {
                    refer.put("businessProperty.addressCode", rightRecord.getEncryptAccount());
                }
            }
        }
    }

}
