<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.iqiyi.vip</groupId>
    <artifactId>vip-fulfillment-center</artifactId>
    <version>6.0.56</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>vip-fulfillment-center-infrastructure</artifactId>
  <packaging>jar</packaging>
  <dependencies>
    <dependency>
      <groupId>com.iqiyi.vip</groupId>
      <artifactId>vip-fulfillment-center-domain</artifactId>
      <version>6.0.56</version>
    </dependency>
    <dependency>
      <groupId>com.iqiyi.vip</groupId>
      <artifactId>vip-fulfillment-center-common</artifactId>
      <version>6.0.56</version>
    </dependency>
  </dependencies>

    <build>
        <plugins>
            <!-- mvn mybatis-generator:generate -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.5</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                    <configurationFile>./generatorConfig.xml</configurationFile>
                </configuration>

                <dependencies>
                    <!-- 数据库驱动 -->
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.45</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
